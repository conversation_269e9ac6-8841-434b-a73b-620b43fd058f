import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Alert, TextInput, ScrollView, Clipboard } from 'react-native';
import { router } from 'expo-router';

export default function SecretPhraseSetupScreen() {
  const [secretPhrase, setSecretPhrase] = useState<string[]>([]);
  const [confirmationPhrase, setConfirmationPhrase] = useState('');
  const [phraseSaved, setPhraseSaved] = useState(false);

  // African language words for secret phrase generation
  const africanWords = [
    // Swahili
    'jambo', 'asante', 'karibu', 'hujambo', 'pole', 'haraka', 'furaha', 'upendo',
    // Zulu
    'sawubona', 'ngiyabonga', 'hamba', 'kulungile', 'injabulo', 'uthando', 'ubuntu',
    // Kikon<PERSON>
    'mbote', 'matondo', 'nzambi', 'kimia', 'luzolo', 'nsangu', 'mvuama',
    // Shona
    'mhoro', 'tatenda', 'rudo', 'mufaro', 'rugare', 'simba', 'chipo',
    // Yoruba
    'bawo', 'ese', 'ife', 'ayo', 'alafia', 'agbon', 'omo',
    // Fon
    'kudo', 'akpe', 'nunana', 'dzidzome', 'fia', 'deka', 'ame',
    // Bambara
    'aw ni baara', 'aw ni ce', 'barka', 'here', 'kanu', 'diya', 'fanga',
    // Sango
    'bara ala', 'singila', 'yeke', 'molenge', 'lingo', 'kodoro', 'wali'
  ];

  useEffect(() => {
    generateSecretPhrase();
  }, []);

  const generateSecretPhrase = () => {
    const shuffled = [...africanWords].sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, 9);
    setSecretPhrase(selected);
    setConfirmationPhrase('');
    setPhraseSaved(false);
  };

  const copyPhrase = () => {
    const phraseString = secretPhrase.join(' ');
    Clipboard.setString(phraseString);
    Alert.alert('Copied!', 'Your secret phrase has been copied to clipboard');
  };

  const handleConfirmation = () => {
    const expectedPhrase = secretPhrase.join(' ');
    if (confirmationPhrase.trim().toLowerCase() === expectedPhrase.toLowerCase()) {
      if (phraseSaved) {
        Alert.alert('Success!', 'Account setup complete. Redirecting to login...', [
          { text: 'OK', onPress: () => router.replace('/(auth)/login') }
        ]);
      } else {
        Alert.alert('Please Confirm', 'Please check "I have saved my phrase" before continuing');
      }
    } else {
      Alert.alert('Incorrect Phrase', 'The phrase you entered does not match. Please try again.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Secret Recovery Phrase</Text>
            <Text style={styles.subtitle}>Save this phrase securely - it's your account backup</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Secret Phrase Setup Screen</Text>
            <Text style={styles.descriptionText}>
              This screen generates 9 randomly selected words from African languages 
              (Swahili, Zulu, Kikongo, Shona, Yoruba, Fon, Bambara, Sango) that serve 
              as the user's recovery phrase for account restoration.
            </Text>
          </View>

          <View style={styles.warningSection}>
            <Text style={styles.warningTitle}>⚠️ CRITICAL SECURITY WARNING</Text>
            <Text style={styles.warningText}>
              • This phrase is the ONLY way to recover your account if you lose access
            </Text>
            <Text style={styles.warningText}>
              • Write it down and store it in a safe place
            </Text>
            <Text style={styles.warningText}>
              • Never share this phrase with anyone
            </Text>
            <Text style={styles.warningText}>
              • MEENA cannot recover your account without this phrase
            </Text>
          </View>

          <View style={styles.phraseSection}>
            <Text style={styles.phraseTitle}>Your Secret Phrase:</Text>
            <View style={styles.phraseContainer}>
              {secretPhrase.map((word, index) => (
                <View key={index} style={styles.wordContainer}>
                  <Text style={styles.wordNumber}>{index + 1}</Text>
                  <Text style={styles.wordText}>{word}</Text>
                </View>
              ))}
            </View>
            
            <View style={styles.phraseActions}>
              <TouchableOpacity style={styles.copyButton} onPress={copyPhrase}>
                <Text style={styles.copyButtonText}>Copy Phrase</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.regenerateButton} onPress={generateSecretPhrase}>
                <Text style={styles.regenerateButtonText}>Generate New</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.confirmationSection}>
            <Text style={styles.confirmationTitle}>Confirm Your Phrase:</Text>
            <TextInput
              style={styles.confirmationInput}
              value={confirmationPhrase}
              onChangeText={setConfirmationPhrase}
              placeholder="Type your secret phrase here to confirm"
              multiline
            />
            
            <View style={styles.checkboxContainer}>
              <TouchableOpacity 
                style={styles.checkbox}
                onPress={() => setPhraseSaved(!phraseSaved)}
              >
                <Text style={styles.checkboxText}>{phraseSaved ? '☑️' : '☐'}</Text>
              </TouchableOpacity>
              <Text style={styles.checkboxLabel}>
                I have saved my secret phrase in a secure location
              </Text>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Next Step:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.replace('/(auth)/login')}
            >
              <Text style={styles.navButtonText}>→ Complete Setup & Go to Login</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.buttons}>
            <TouchableOpacity 
              style={[styles.button, styles.confirmButton]}
              onPress={handleConfirmation}
            >
              <Text style={styles.confirmButtonText}>Confirm & Complete Setup</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity onPress={() => router.back()}>
              <Text style={styles.backText}>← Back to ID Generation</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  warningSection: {
    backgroundColor: '#ffebee',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#c62828',
    marginBottom: 10,
  },
  warningText: {
    fontSize: 14,
    color: '#d32f2f',
    marginBottom: 5,
    lineHeight: 20,
  },
  phraseSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  phraseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  phraseContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  wordContainer: {
    width: '30%',
    backgroundColor: '#e3f2fd',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  wordNumber: {
    fontSize: 12,
    color: '#1976d2',
    fontWeight: 'bold',
  },
  wordText: {
    fontSize: 16,
    color: '#0d47a1',
    fontWeight: 'bold',
    marginTop: 5,
  },
  phraseActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  copyButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  copyButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  regenerateButton: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  regenerateButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  confirmationSection: {
    marginBottom: 20,
  },
  confirmationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  confirmationInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: 'white',
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: 15,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    marginRight: 10,
  },
  checkboxText: {
    fontSize: 20,
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  buttons: {
    marginBottom: 20,
  },
  button: {
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  confirmButton: {
    backgroundColor: '#4CAF50',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
