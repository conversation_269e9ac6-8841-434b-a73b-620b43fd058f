import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, TextInput, Alert, ScrollView } from 'react-native';
import { router } from 'expo-router';

export default function RegistrationScreen() {
  const [phoneOrEmail, setPhoneOrEmail] = useState('');
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);

  const handleNext = () => {
    if (phoneOrEmail && name && password && termsAccepted) {
      Alert.alert('Registration Data Collected', 'Proceeding to ID generation...', [
        { text: 'OK', onPress: () => router.push('/(auth)/id-generation') }
      ]);
    } else {
      Alert.alert('Incomplete Form', 'Please fill all fields and accept terms');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Create MEENA Account</Text>
            <Text style={styles.subtitle}>Join the secure social network</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Registration Screen</Text>
            <Text style={styles.descriptionText}>
              This screen collects basic user information for account creation. A unique 
              9-character alphanumeric ID will be auto-generated, and users will set up 
              their security phrase in the next steps.
            </Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Phone Number or Email</Text>
              <TextInput
                style={styles.input}
                value={phoneOrEmail}
                onChangeText={setPhoneOrEmail}
                placeholder="Enter phone number or email"
                keyboardType="email-address"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Full Name</Text>
              <TextInput
                style={styles.input}
                value={name}
                onChangeText={setName}
                placeholder="Enter your full name"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Password</Text>
              <TextInput
                style={styles.input}
                value={password}
                onChangeText={setPassword}
                placeholder="Create a strong password"
                secureTextEntry
              />
            </View>

            <View style={styles.idPreview}>
              <Text style={styles.idLabel}>Your unique ID will be auto-generated:</Text>
              <Text style={styles.idExample}>Example: ABC123XYZ</Text>
              <Text style={styles.idNote}>
                This 9-character ID will be used for login and identification
              </Text>
            </View>

            <View style={styles.termsSection}>
              <TouchableOpacity 
                style={styles.checkbox}
                onPress={() => setTermsAccepted(!termsAccepted)}
              >
                <Text style={styles.checkboxText}>{termsAccepted ? '☑️' : '☐'}</Text>
              </TouchableOpacity>
              <View style={styles.termsText}>
                <Text style={styles.termsLabel}>I accept the:</Text>
                <TouchableOpacity onPress={() => Alert.alert('Community Rules', 'Community Rules would be displayed here')}>
                  <Text style={styles.termsLink}>• Community Rules</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => Alert.alert('Terms of Use', 'Terms of Use would be displayed here')}>
                  <Text style={styles.termsLink}>• Terms of Use</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => Alert.alert('Privacy Policy', 'Privacy Policy would be displayed here')}>
                  <Text style={styles.termsLink}>• Privacy Policy</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Flow:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(auth)/id-generation')}
            >
              <Text style={styles.navButtonText}>→ Next: ID Generation Screen</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.buttons}>
            <TouchableOpacity 
              style={[styles.button, styles.nextButton]}
              onPress={handleNext}
            >
              <Text style={styles.nextButtonText}>Next</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.button, styles.loginButton]}
              onPress={() => router.push('/(auth)/login')}
            >
              <Text style={styles.loginButtonText}>Already have an account? Login</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity onPress={() => router.back()}>
              <Text style={styles.backText}>← Back to Onboarding</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  form: {
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  idPreview: {
    backgroundColor: '#fff3e0',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  idLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e65100',
    marginBottom: 5,
  },
  idExample: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ff9800',
    marginBottom: 5,
  },
  idNote: {
    fontSize: 14,
    color: '#f57c00',
  },
  termsSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  checkbox: {
    marginRight: 10,
    marginTop: 2,
  },
  checkboxText: {
    fontSize: 20,
  },
  termsText: {
    flex: 1,
  },
  termsLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 5,
  },
  termsLink: {
    fontSize: 16,
    color: '#2196F3',
    marginBottom: 3,
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  buttons: {
    marginBottom: 20,
  },
  button: {
    paddingVertical: 15,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  nextButton: {
    backgroundColor: '#4CAF50',
  },
  loginButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#2196F3',
  },
  nextButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loginButtonText: {
    color: '#2196F3',
    fontSize: 16,
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
