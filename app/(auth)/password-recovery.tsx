import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, TextInput, Alert, ScrollView } from 'react-native';
import { router } from 'expo-router';

export default function PasswordRecoveryScreen() {
  const [userId, setUserId] = useState('');
  const [secretPhrase, setSecretPhrase] = useState('');
  const [pin, setPin] = useState('');

  const handleRecovery = () => {
    if (userId.length === 9 && secretPhrase.trim().length > 0) {
      Alert.alert('Verification Successful', 'Proceeding to password reset...', [
        { text: 'OK', onPress: () => router.push('/(auth)/password-reset') }
      ]);
    } else {
      Alert.alert('Invalid Input', 'Please enter valid ID and secret phrase');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Account Recovery</Text>
            <Text style={styles.subtitle}>Recover your MEENA account access</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Password Recovery Screen</Text>
            <Text style={styles.descriptionText}>
              This screen allows users to recover their account by verifying their 
              unique ID, secret phrase (9 African language words), and PIN (if set). 
              Successful verification leads to password reset.
            </Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Your MEENA ID (9 characters)</Text>
              <TextInput
                style={styles.input}
                value={userId}
                onChangeText={setUserId}
                placeholder="Enter your 9-character ID"
                maxLength={9}
                autoCapitalize="characters"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Secret Recovery Phrase</Text>
              <TextInput
                style={[styles.input, styles.multilineInput]}
                value={secretPhrase}
                onChangeText={setSecretPhrase}
                placeholder="Enter your 9-word secret phrase (separated by spaces)"
                multiline
                numberOfLines={3}
              />
              <Text style={styles.hint}>
                Enter the 9 African language words you saved during registration
              </Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>PIN (if set)</Text>
              <TextInput
                style={styles.input}
                value={pin}
                onChangeText={setPin}
                placeholder="Enter your PIN (optional)"
                secureTextEntry
                keyboardType="numeric"
                maxLength={6}
              />
              <Text style={styles.hint}>
                Only required if you previously set up a PIN for your account
              </Text>
            </View>
          </View>

          <View style={styles.recoveryInfo}>
            <Text style={styles.recoveryTitle}>Recovery Requirements:</Text>
            <Text style={styles.recoveryItem}>✓ Your unique 9-character MEENA ID</Text>
            <Text style={styles.recoveryItem}>✓ Your 9-word secret recovery phrase</Text>
            <Text style={styles.recoveryItem}>• PIN verification (if previously set)</Text>
            <Text style={styles.recoveryNote}>
              Account recovery is possible within 60 days of deletion
            </Text>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Options:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(auth)/password-reset')}
            >
              <Text style={styles.navButtonText}>→ Password Reset (after verification)</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(auth)/login')}
            >
              <Text style={styles.navButtonText}>→ Back to Login</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.buttons}>
            <TouchableOpacity 
              style={[styles.button, styles.recoverButton]}
              onPress={handleRecovery}
            >
              <Text style={styles.recoverButtonText}>Recover Account</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.button, styles.helpButton]}
              onPress={() => Alert.alert('Recovery Help', 'Contact support if you cannot remember your secret phrase or ID')}
            >
              <Text style={styles.helpButtonText}>Need Help?</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity onPress={() => router.back()}>
              <Text style={styles.backText}>← Back to Login</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  form: {
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  hint: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
    fontStyle: 'italic',
  },
  recoveryInfo: {
    backgroundColor: '#fff3e0',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  recoveryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e65100',
    marginBottom: 10,
  },
  recoveryItem: {
    fontSize: 14,
    color: '#f57c00',
    marginBottom: 5,
    lineHeight: 20,
  },
  recoveryNote: {
    fontSize: 14,
    color: '#ff9800',
    marginTop: 10,
    fontStyle: 'italic',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  buttons: {
    marginBottom: 20,
  },
  button: {
    paddingVertical: 15,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  recoverButton: {
    backgroundColor: '#4CAF50',
  },
  helpButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#FF9800',
  },
  recoverButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  helpButtonText: {
    color: '#FF9800',
    fontSize: 16,
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
