import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Alert, Clipboard } from 'react-native';
import { router } from 'expo-router';

export default function IdGenerationScreen() {
  const [generatedId, setGeneratedId] = useState('');

  useEffect(() => {
    // Generate a random 9-character alphanumeric ID
    const generateId = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = '';
      for (let i = 0; i < 9; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    };
    
    setGeneratedId(generateId());
  }, []);

  const copyToClipboard = () => {
    Clipboard.setString(generatedId);
    Alert.alert('Copied!', 'Your ID has been copied to clipboard');
  };

  const handleContinue = () => {
    Alert.alert('ID Confirmed', 'Proceeding to secret phrase setup...', [
      { text: 'OK', onPress: () => router.push('/(auth)/secret-phrase-setup') }
    ]);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Your Unique MEENA ID</Text>
          <Text style={styles.subtitle}>This is your permanent identifier</Text>
        </View>

        <View style={styles.description}>
          <Text style={styles.descriptionTitle}>ID Generation Screen</Text>
          <Text style={styles.descriptionText}>
            This screen displays the auto-generated unique 9-character alphanumeric ID 
            that will be used for login and identification. Users should save this ID 
            securely as it cannot be changed later.
          </Text>
        </View>

        <View style={styles.idSection}>
          <Text style={styles.idLabel}>Your MEENA ID:</Text>
          <View style={styles.idContainer}>
            <Text style={styles.idText}>{generatedId}</Text>
            <TouchableOpacity 
              style={styles.copyButton}
              onPress={copyToClipboard}
            >
              <Text style={styles.copyButtonText}>Copy</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.explanation}>
          <Text style={styles.explanationTitle}>Important Information:</Text>
          <Text style={styles.explanationItem}>• This ID is globally unique and cannot be changed</Text>
          <Text style={styles.explanationItem}>• Use this ID to log into your MEENA account</Text>
          <Text style={styles.explanationItem}>• Share this ID with others to connect on MEENA</Text>
          <Text style={styles.explanationItem}>• Keep this ID safe - you'll need it for account recovery</Text>
          <Text style={styles.explanationItem}>• This ID is different from your public username (PID)</Text>
        </View>

        <View style={styles.usage}>
          <Text style={styles.usageTitle}>How to use your ID:</Text>
          <Text style={styles.usageText}>
            Your MEENA ID is used for secure login and account identification. 
            It's like your account number - private and secure. Your public 
            profile will have a separate username (PID) that others can see.
          </Text>
        </View>

        <View style={styles.navigation}>
          <Text style={styles.navigationTitle}>Next Step:</Text>
          <TouchableOpacity 
            style={styles.navButton}
            onPress={() => router.push('/(auth)/secret-phrase-setup')}
          >
            <Text style={styles.navButtonText}>→ Continue to Secret Phrase Setup</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.buttons}>
          <TouchableOpacity 
            style={[styles.button, styles.continueButton]}
            onPress={handleContinue}
          >
            <Text style={styles.continueButtonText}>Continue</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.button, styles.regenerateButton]}
            onPress={() => {
              const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
              let result = '';
              for (let i = 0; i < 9; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
              }
              setGeneratedId(result);
              Alert.alert('New ID Generated', 'A new unique ID has been generated for you');
            }}
          >
            <Text style={styles.regenerateButtonText}>Generate New ID</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.backText}>← Back to Registration</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  idSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  idLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  idContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#2196F3',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  idText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
    letterSpacing: 2,
    marginRight: 15,
  },
  copyButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 8,
  },
  copyButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  explanation: {
    backgroundColor: '#fff3e0',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  explanationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e65100',
    marginBottom: 10,
  },
  explanationItem: {
    fontSize: 14,
    color: '#f57c00',
    marginBottom: 5,
    lineHeight: 20,
  },
  usage: {
    backgroundColor: '#e1f5fe',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  usageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0277bd',
    marginBottom: 10,
  },
  usageText: {
    fontSize: 14,
    color: '#0288d1',
    lineHeight: 20,
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  buttons: {
    marginBottom: 20,
  },
  button: {
    paddingVertical: 15,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  continueButton: {
    backgroundColor: '#4CAF50',
  },
  regenerateButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#FF9800',
  },
  continueButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  regenerateButtonText: {
    color: '#FF9800',
    fontSize: 16,
  },
  footer: {
    alignItems: 'center',
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
