import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView } from 'react-native';
import { router } from 'expo-router';

export default function OnboardingScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.logo}>MEENA</Text>
          <Text style={styles.tagline}>Secure Social Networking</Text>
        </View>

        <View style={styles.description}>
          <Text style={styles.descriptionTitle}>Welcome to MEENA</Text>
          <Text style={styles.descriptionText}>
            A secure social networking and messaging platform with end-to-end encryption, 
            dual-profile system, and comprehensive privacy features.
          </Text>
          
          <Text style={styles.featuresTitle}>Key Features:</Text>
          <Text style={styles.featureItem}>• End-to-end encrypted messaging</Text>
          <Text style={styles.featureItem}>• Dual-profile system (Private & Public)</Text>
          <Text style={styles.featureItem}>• Secure group and channel creation</Text>
          <Text style={styles.featureItem}>• Rich story functionality</Text>
          <Text style={styles.featureItem}>• Advanced security features</Text>
        </View>

        <View style={styles.navigation}>
          <Text style={styles.navigationTitle}>Navigation Options:</Text>
          <TouchableOpacity 
            style={styles.navButton}
            onPress={() => router.push('/(auth)/login')}
          >
            <Text style={styles.navButtonText}>→ Login (Existing Users)</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.navButton}
            onPress={() => router.push('/(auth)/registration')}
          >
            <Text style={styles.navButtonText}>→ Sign Up (New Users)</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.buttons}>
          <TouchableOpacity 
            style={[styles.button, styles.loginButton]}
            onPress={() => router.push('/(auth)/login')}
          >
            <Text style={styles.loginButtonText}>Login</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.button, styles.signupButton]}
            onPress={() => router.push('/(auth)/registration')}
          >
            <Text style={styles.signupButtonText}>Sign Up</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Language: English</Text>
          <Text style={styles.footerText}>Biometric login available for returning users</Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
  },
  logo: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 10,
  },
  tagline: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    flex: 1,
    marginTop: 40,
  },
  descriptionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#666',
    marginBottom: 20,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  featureItem: {
    fontSize: 16,
    lineHeight: 24,
    color: '#666',
    marginBottom: 5,
  },
  navigation: {
    marginTop: 30,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 8,
  },
  navButtonText: {
    fontSize: 16,
    color: '#1976d2',
  },
  buttons: {
    marginTop: 30,
  },
  button: {
    paddingVertical: 15,
    borderRadius: 10,
    marginBottom: 15,
    alignItems: 'center',
  },
  loginButton: {
    backgroundColor: '#2196F3',
  },
  signupButton: {
    backgroundColor: '#4CAF50',
  },
  loginButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  signupButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 5,
  },
});
