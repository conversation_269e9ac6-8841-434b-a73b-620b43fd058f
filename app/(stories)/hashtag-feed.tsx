import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView } from 'react-native';
import { router } from 'expo-router';

export default function HashtagFeedScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>#Hashtag Feed</Text>
          <Text style={styles.subtitle}>Discover trending content</Text>
        </View>

        <View style={styles.description}>
          <Text style={styles.descriptionTitle}>Hashtag Feed Screen</Text>
          <Text style={styles.descriptionText}>
            Feed of posts filtered by specific hashtags, allowing users 
            to discover trending content and explore topics of interest.
          </Text>
        </View>

        <View style={styles.feedArea}>
          <Text style={styles.feedPlaceholder}>#️⃣ Hashtag content would appear here</Text>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.backText}>← Back to Home</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  feedArea: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  feedPlaceholder: {
    fontSize: 18,
    color: '#999',
  },
  footer: {
    alignItems: 'center',
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
