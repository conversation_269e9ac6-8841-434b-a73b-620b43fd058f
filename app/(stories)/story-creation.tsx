import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { router } from 'expo-router';

export default function StoryCreationScreen() {
  const [privacySetting, setPrivacySetting] = useState('both');

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Create Story</Text>
            <Text style={styles.subtitle}>Share your moment with rich media</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Story Creation Flow</Text>
            <Text style={styles.descriptionText}>
              This screen provides comprehensive story creation tools including media selection, 
              editing tools (text overlay, media layering, backgrounds, music), location tagging, 
              and privacy settings for dual-profile sharing.
            </Text>
          </View>

          <View style={styles.mediaSection}>
            <Text style={styles.sectionTitle}>Media Selection:</Text>
            
            <TouchableOpacity 
              style={styles.mediaOption}
              onPress={() => router.push('/(stories)/media-selection')}
            >
              <Text style={styles.mediaIcon}>📷</Text>
              <Text style={styles.mediaText}>Camera</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.mediaOption}
              onPress={() => router.push('/(stories)/media-selection')}
            >
              <Text style={styles.mediaIcon}>🖼️</Text>
              <Text style={styles.mediaText}>Gallery</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.editingTools}>
            <Text style={styles.sectionTitle}>Editing Tools:</Text>
            
            <View style={styles.toolsGrid}>
              <TouchableOpacity 
                style={styles.tool}
                onPress={() => router.push('/(stories)/editing-screen')}
              >
                <Text style={styles.toolIcon}>📝</Text>
                <Text style={styles.toolText}>Text Overlay</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.tool}
                onPress={() => router.push('/(stories)/editing-screen')}
              >
                <Text style={styles.toolIcon}>🎨</Text>
                <Text style={styles.toolText}>Media Layering</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.tool}
                onPress={() => router.push('/(stories)/editing-screen')}
              >
                <Text style={styles.toolIcon}>🌈</Text>
                <Text style={styles.toolText}>Backgrounds</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.tool}
                onPress={() => router.push('/(stories)/editing-screen')}
              >
                <Text style={styles.toolIcon}>🎵</Text>
                <Text style={styles.toolText}>Music</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.tool}
                onPress={() => router.push('/(stories)/editing-screen')}
              >
                <Text style={styles.toolIcon}>📍</Text>
                <Text style={styles.toolText}>Location</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.privacySection}>
            <Text style={styles.sectionTitle}>Privacy Settings:</Text>
            
            <TouchableOpacity 
              style={[styles.privacyOption, privacySetting === 'private' && styles.selectedPrivacy]}
              onPress={() => setPrivacySetting('private')}
            >
              <Text style={styles.privacyText}>Private Profile Only</Text>
              <Text style={styles.checkmark}>{privacySetting === 'private' ? '✓' : ''}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.privacyOption, privacySetting === 'public' && styles.selectedPrivacy]}
              onPress={() => setPrivacySetting('public')}
            >
              <Text style={styles.privacyText}>Public Page Only</Text>
              <Text style={styles.checkmark}>{privacySetting === 'public' ? '✓' : ''}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.privacyOption, privacySetting === 'both' && styles.selectedPrivacy]}
              onPress={() => setPrivacySetting('both')}
            >
              <Text style={styles.privacyText}>Both Profiles</Text>
              <Text style={styles.checkmark}>{privacySetting === 'both' ? '✓' : ''}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Creation Flow:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(stories)/media-selection')}
            >
              <Text style={styles.navButtonText}>→ Media Selection</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(stories)/editing-screen')}
            >
              <Text style={styles.navButtonText}>→ Editing Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(stories)/privacy-settings')}
            >
              <Text style={styles.navButtonText}>→ Privacy Settings</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(stories)/preview-screen')}
            >
              <Text style={styles.navButtonText}>→ Preview Screen</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity 
              style={[styles.actionButton, styles.continueButton]}
              onPress={() => router.push('/(stories)/media-selection')}
            >
              <Text style={styles.continueButtonText}>📷 Select Media</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.previewButton]}
              onPress={() => router.push('/(stories)/preview-screen')}
            >
              <Text style={styles.previewButtonText}>👁️ Preview Story</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity onPress={() => router.back()}>
              <Text style={styles.backText}>← Back to Home</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  mediaSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  mediaOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    marginBottom: 10,
  },
  mediaIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  mediaText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
  editingTools: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  toolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  tool: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  toolIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  toolText: {
    fontSize: 14,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  privacySection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  privacyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    marginBottom: 10,
  },
  selectedPrivacy: {
    borderColor: '#2196F3',
    backgroundColor: '#e3f2fd',
  },
  privacyText: {
    fontSize: 16,
    color: '#333',
  },
  checkmark: {
    fontSize: 20,
    color: '#2196F3',
    fontWeight: 'bold',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  actions: {
    marginBottom: 20,
  },
  actionButton: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  continueButton: {
    backgroundColor: '#4CAF50',
  },
  previewButton: {
    backgroundColor: '#2196F3',
  },
  continueButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  previewButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
