import { Stack } from 'expo-router';

export default function StoriesLayout() {
  return (
    <Stack>
      <Stack.Screen name="story-creation" options={{ headerShown: false }} />
      <Stack.Screen name="story-viewing" options={{ headerShown: false }} />
      <Stack.Screen name="post-detail" options={{ headerShown: false }} />
      <Stack.Screen name="hashtag-feed" options={{ headerShown: false }} />
      <Stack.Screen name="media-selection" options={{ headerShown: false }} />
      <Stack.Screen name="editing-screen" options={{ headerShown: false }} />
      <Stack.Screen name="privacy-settings" options={{ headerShown: false }} />
      <Stack.Screen name="preview-screen" options={{ headerShown: false }} />
    </Stack>
  );
}
