import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView } from 'react-native';
import { router } from 'expo-router';

export default function StoryViewingScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Story Viewing</Text>
          <Text style={styles.subtitle}>Full-screen story display</Text>
        </View>

        <View style={styles.description}>
          <Text style={styles.descriptionTitle}>Story Viewing Screen</Text>
          <Text style={styles.descriptionText}>
            Full-screen story display with progress indicator, reaction options, 
            reply functionality, profile access, and reporting option.
          </Text>
        </View>

        <View style={styles.storyDisplay}>
          <Text style={styles.storyContent}>📖 Story Content Display Area</Text>
          <View style={styles.progressBar}>
            <View style={styles.progress} />
          </View>
        </View>

        <View style={styles.controls}>
          <TouchableOpacity style={styles.controlButton}>
            <Text style={styles.controlText}>❤️ React</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.controlButton}
            onPress={() => router.push('/(messaging)/chat-detail')}
          >
            <Text style={styles.controlText}>💬 Reply</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.controlButton}
            onPress={() => router.push('/(profile)/user-profile')}
          >
            <Text style={styles.controlText}>👤 Profile</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.controlButton}
            onPress={() => router.push('/(security)/report-content')}
          >
            <Text style={styles.controlText}>🚫 Report</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.backText}>← Back to Home</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#fff',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#ccc',
  },
  storyDisplay: {
    flex: 1,
    backgroundColor: '#333',
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  storyContent: {
    fontSize: 24,
    color: '#fff',
    textAlign: 'center',
  },
  progressBar: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
  },
  progress: {
    width: '60%',
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  controlButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
  },
  controlText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
  },
  backText: {
    color: '#ccc',
    fontSize: 16,
  },
});
