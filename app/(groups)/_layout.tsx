import { Stack } from 'expo-router';

export default function GroupsLayout() {
  return (
    <Stack>
      <Stack.Screen name="group-creation" options={{ headerShown: false }} />
      <Stack.Screen name="channel-creation" options={{ headerShown: false }} />
      <Stack.Screen name="group-management" options={{ headerShown: false }} />
      <Stack.Screen name="channel-management" options={{ headerShown: false }} />
      <Stack.Screen name="subgroup-creation" options={{ headerShown: false }} />
      <Stack.Screen name="terms-acceptance" options={{ headerShown: false }} />
      <Stack.Screen name="payment-screen" options={{ headerShown: false }} />
    </Stack>
  );
}
