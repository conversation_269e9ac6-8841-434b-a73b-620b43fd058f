import { router } from 'expo-router';
import React from 'react';
import { Safe<PERSON>reaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function PublicProfileScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Public Profile</Text>
            <Text style={styles.subtitle}>Social Media-style public presence</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Public Profile Screen</Text>
            <Text style={styles.descriptionText}>
              This screen displays the user's public profile in social media style, 
              featuring cover photo, profile photo, display name, PID (username), 
              bio, website links, follower/following counts, content posts grid, 
              and verification badges.
            </Text>
          </View>

          <View style={styles.profileCard}>
            <View style={styles.coverPhoto}>
              <Text style={styles.coverText}>Cover Photo</Text>
            </View>
            
            <View style={styles.profileSection}>
              <View style={styles.profilePhoto}>
                <Text style={styles.photoText}>ME</Text>
              </View>
              
              <View style={styles.profileInfo}>
                <Text style={styles.displayName}>Your Public Name</Text>
                <Text style={styles.pid}>@your_username</Text>
                <Text style={styles.bio}>
                  Your bio goes here. Share something interesting about yourself!
                </Text>
                <Text style={styles.website}>🔗 yourwebsite.com</Text>
              </View>

              <View style={styles.badges}>
                <View style={styles.verifiedBadge}>
                  <Text style={styles.badgeText}>✓</Text>
                </View>
                <View style={styles.goldBadge}>
                  <Text style={styles.badgeText}>🏆</Text>
                </View>
              </View>
            </View>

            <View style={styles.stats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>1.2K</Text>
                <Text style={styles.statLabel}>Followers</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>856</Text>
                <Text style={styles.statLabel}>Following</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>42</Text>
                <Text style={styles.statLabel}>Posts</Text>
              </View>
            </View>
          </View>

          <View style={styles.postsGrid}>
            <Text style={styles.sectionTitle}>Content Posts</Text>
            <View style={styles.gridContainer}>
              <View style={styles.postItem}>
                <Text style={styles.postText}>Post 1</Text>
              </View>
              <View style={styles.postItem}>
                <Text style={styles.postText}>Post 2</Text>
              </View>
              <View style={styles.postItem}>
                <Text style={styles.postText}>Post 3</Text>
              </View>
              <View style={styles.postItem}>
                <Text style={styles.postText}>Post 4</Text>
              </View>
              <View style={styles.postItem}>
                <Text style={styles.postText}>Post 5</Text>
              </View>
              <View style={styles.postItem}>
                <Text style={styles.postText}>Post 6</Text>
              </View>
            </View>
          </View>

          <View style={styles.componentsSection}>
            <Text style={styles.componentsTitle}>Profile Components:</Text>
            
            <View style={styles.component}>
              <Text style={styles.componentName}>🖼️ Cover Photo</Text>
              <Text style={styles.componentDesc}>Large banner image at top</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>👤 Profile Photo</Text>
              <Text style={styles.componentDesc}>Circular profile picture</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📝 Bio & Links</Text>
              <Text style={styles.componentDesc}>Personal description and website links</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📊 Follower Counts</Text>
              <Text style={styles.componentDesc}>Followers, following, and posts statistics</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📱 Content Grid</Text>
              <Text style={styles.componentDesc}>Grid layout of user's posts</Text>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Options:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/edit-public-profile')}
            >
              <Text style={styles.navButtonText}>→ Edit Public Profile</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/content-preferences')}
            >
              <Text style={styles.navButtonText}>→ Content Preferences</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/business-account-setup')}
            >
              <Text style={styles.navButtonText}>→ Business Account Setup</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/verification-request')}
            >
              <Text style={styles.navButtonText}>→ Verification Request</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity 
              style={[styles.actionButton, styles.editButton]}
              onPress={() => router.push('/(profile)/edit-public-profile')}
            >
              <Text style={styles.editButtonText}>✏️ Edit Profile</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.contentButton]}
              onPress={() => router.push('/(profile)/content-preferences')}
            >
              <Text style={styles.contentButtonText}>🎨 Content Preferences</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.businessButton]}
              onPress={() => router.push('/(profile)/business-account-setup')}
            >
              <Text style={styles.businessButtonText}>🏢 Business Setup</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.verificationButton]}
              onPress={() => router.push('/(profile)/verification-request')}
            >
              <Text style={styles.verificationButtonText}>✓ Request Verification</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity onPress={() => router.back()}>
              <Text style={styles.backText}>← Back to Profile</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  profileCard: {
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  coverPhoto: {
    height: 120,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
  },
  coverText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  profileSection: {
    padding: 20,
    alignItems: 'center',
  },
  profilePhoto: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -40,
    marginBottom: 15,
    borderWidth: 4,
    borderColor: 'white',
  },
  photoText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileInfo: {
    alignItems: 'center',
    marginBottom: 15,
  },
  displayName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  pid: {
    fontSize: 16,
    color: '#2196F3',
    marginBottom: 10,
  },
  bio: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
    lineHeight: 22,
  },
  website: {
    fontSize: 16,
    color: '#2196F3',
  },
  badges: {
    flexDirection: 'row',
    gap: 10,
  },
  verifiedBadge: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  goldBadge: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#FFD700',
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  postsGrid: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  postItem: {
    width: '30%',
    aspectRatio: 1,
    backgroundColor: '#e0e0e0',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  postText: {
    fontSize: 12,
    color: '#666',
  },
  componentsSection: {
    marginBottom: 20,
  },
  componentsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  component: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  componentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  componentDesc: {
    fontSize: 14,
    color: '#666',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  actions: {
    marginBottom: 20,
  },
  actionButton: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  editButton: {
    backgroundColor: '#2196F3',
  },
  contentButton: {
    backgroundColor: '#9C27B0',
  },
  businessButton: {
    backgroundColor: '#FF9800',
  },
  verificationButton: {
    backgroundColor: '#4CAF50',
  },
  editButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  contentButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  businessButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  verificationButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
