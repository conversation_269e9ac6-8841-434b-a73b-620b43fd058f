import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { router } from 'expo-router';

export default function PrivateProfileScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Private Profile</Text>
            <Text style={styles.subtitle}>Telegram-style secure profile</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Private Profile Screen</Text>
            <Text style={styles.descriptionText}>
              This screen displays the user's private profile in Telegram-style format, 
              showing profile photo, display name, online status, unique ID, contact 
              information, security phrase indicator, and Gold Membership badge.
            </Text>
          </View>

          <View style={styles.profileCard}>
            <View style={styles.profilePhoto}>
              <Text style={styles.photoText}>ME</Text>
            </View>
            
            <View style={styles.profileInfo}>
              <Text style={styles.displayName}>Your Display Name</Text>
              <Text style={styles.status}>🟢 Online</Text>
              <Text style={styles.userId}>ID: ABC123XYZ</Text>
            </View>

            <View style={styles.badges}>
              <View style={styles.goldBadge}>
                <Text style={styles.badgeText}>🏆 Gold</Text>
              </View>
            </View>
          </View>

          <View style={styles.contactInfo}>
            <Text style={styles.sectionTitle}>Contact Information</Text>
            
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>📱 Phone:</Text>
              <Text style={styles.infoValue}>+****************</Text>
            </View>
            
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>📧 Email:</Text>
              <Text style={styles.infoValue}><EMAIL></Text>
            </View>
            
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>🔐 Security Phrase:</Text>
              <Text style={styles.infoValue}>✓ Configured</Text>
            </View>
          </View>

          <View style={styles.componentsSection}>
            <Text style={styles.componentsTitle}>Profile Components:</Text>
            
            <View style={styles.component}>
              <Text style={styles.componentName}>📷 Profile Photo</Text>
              <Text style={styles.componentDesc}>User's private profile picture</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>👤 Display Name</Text>
              <Text style={styles.componentDesc}>Private profile display name</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>🟢 Status Indicator</Text>
              <Text style={styles.componentDesc}>Online/last seen status</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>🆔 ID Display</Text>
              <Text style={styles.componentDesc}>Unique 9-character identifier</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>🔐 Security Phrase Indicator</Text>
              <Text style={styles.componentDesc}>Shows if recovery phrase is set</Text>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Options:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/edit-private-profile')}
            >
              <Text style={styles.navButtonText}>→ Edit Private Profile</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(security)/security-settings')}
            >
              <Text style={styles.navButtonText}>→ Security Settings</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(security)/privacy-settings')}
            >
              <Text style={styles.navButtonText}>→ Privacy Settings</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/verification-request')}
            >
              <Text style={styles.navButtonText}>→ Verification Request</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity 
              style={[styles.actionButton, styles.editButton]}
              onPress={() => router.push('/(profile)/edit-private-profile')}
            >
              <Text style={styles.editButtonText}>✏️ Edit Profile</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.securityButton]}
              onPress={() => router.push('/(security)/security-settings')}
            >
              <Text style={styles.securityButtonText}>🔒 Security Settings</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.privacyButton]}
              onPress={() => router.push('/(security)/privacy-settings')}
            >
              <Text style={styles.privacyButtonText}>🛡️ Privacy Settings</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.verificationButton]}
              onPress={() => router.push('/(profile)/verification-request')}
            >
              <Text style={styles.verificationButtonText}>✓ Request Verification</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity onPress={() => router.back()}>
              <Text style={styles.backText}>← Back to Profile</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  profileCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    alignItems: 'center',
  },
  profilePhoto: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  photoText: {
    color: 'white',
    fontSize: 32,
    fontWeight: 'bold',
  },
  profileInfo: {
    alignItems: 'center',
    marginBottom: 15,
  },
  displayName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  status: {
    fontSize: 16,
    color: '#4CAF50',
    marginBottom: 5,
  },
  userId: {
    fontSize: 16,
    color: '#666',
    fontFamily: 'monospace',
  },
  badges: {
    flexDirection: 'row',
  },
  goldBadge: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  badgeText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  contactInfo: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  infoLabel: {
    fontSize: 16,
    color: '#666',
    width: 100,
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  componentsSection: {
    marginBottom: 20,
  },
  componentsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  component: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  componentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  componentDesc: {
    fontSize: 14,
    color: '#666',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  actions: {
    marginBottom: 20,
  },
  actionButton: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  editButton: {
    backgroundColor: '#2196F3',
  },
  securityButton: {
    backgroundColor: '#FF9800',
  },
  privacyButton: {
    backgroundColor: '#9C27B0',
  },
  verificationButton: {
    backgroundColor: '#4CAF50',
  },
  editButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  securityButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  privacyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  verificationButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
