import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView, Alert } from 'react-native';
import { router } from 'expo-router';

export default function GoldMembershipScreen() {
  const [paymentMethod, setPaymentMethod] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);

  const handlePurchase = () => {
    if (!paymentMethod) {
      Alert.alert('Payment Method Required', 'Please select a payment method');
      return;
    }
    if (!termsAccepted) {
      Alert.alert('Terms Required', 'Please accept the terms and conditions');
      return;
    }
    
    Alert.alert('Purchase Initiated', 'Redirecting to payment processing...', [
      { text: 'OK', onPress: () => router.push('/(profile)/payment-processing') }
    ]);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Gold Membership</Text>
            <Text style={styles.subtitle}>Unlock premium features for life</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Gold Membership Screen</Text>
            <Text style={styles.descriptionText}>
              This screen displays Gold Membership benefits, pricing (€80 one-time), 
              payment method selection, and terms acceptance. Gold Membership provides 
              lifetime access to premium features.
            </Text>
          </View>

          <View style={styles.priceCard}>
            <Text style={styles.priceTitle}>🏆 Gold Membership</Text>
            <Text style={styles.price}>€80</Text>
            <Text style={styles.priceSubtitle}>One-time payment • Lifetime access</Text>
          </View>

          <View style={styles.benefitsSection}>
            <Text style={styles.benefitsTitle}>Premium Benefits:</Text>
            
            <View style={styles.benefit}>
              <Text style={styles.benefitIcon}>🔄</Text>
              <Text style={styles.benefitText}>Unlimited group/channel creation</Text>
            </View>
            
            <View style={styles.benefit}>
              <Text style={styles.benefitIcon}>📞</Text>
              <Text style={styles.benefitText}>Unlimited calls (voice & video)</Text>
            </View>
            
            <View style={styles.benefit}>
              <Text style={styles.benefitIcon}>🗑️</Text>
              <Text style={styles.benefitText}>Remote profile deletion capability</Text>
            </View>
            
            <View style={styles.benefit}>
              <Text style={styles.benefitIcon}>🚫</Text>
              <Text style={styles.benefitText}>Unsubscribe from default accounts</Text>
            </View>
            
            <View style={styles.benefit}>
              <Text style={styles.benefitIcon}>⭐</Text>
              <Text style={styles.benefitText}>Gold badge on profile</Text>
            </View>
            
            <View style={styles.benefit}>
              <Text style={styles.benefitIcon}>🎯</Text>
              <Text style={styles.benefitText}>Priority customer support</Text>
            </View>
          </View>

          <View style={styles.paymentSection}>
            <Text style={styles.paymentTitle}>Payment Method:</Text>
            
            <TouchableOpacity 
              style={[styles.paymentOption, paymentMethod === 'card' && styles.selectedPayment]}
              onPress={() => setPaymentMethod('card')}
            >
              <Text style={styles.paymentIcon}>💳</Text>
              <Text style={styles.paymentText}>Credit/Debit Card</Text>
              <Text style={styles.checkmark}>{paymentMethod === 'card' ? '✓' : ''}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.paymentOption, paymentMethod === 'paypal' && styles.selectedPayment]}
              onPress={() => setPaymentMethod('paypal')}
            >
              <Text style={styles.paymentIcon}>🅿️</Text>
              <Text style={styles.paymentText}>PayPal</Text>
              <Text style={styles.checkmark}>{paymentMethod === 'paypal' ? '✓' : ''}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.paymentOption, paymentMethod === 'apple' && styles.selectedPayment]}
              onPress={() => setPaymentMethod('apple')}
            >
              <Text style={styles.paymentIcon}>🍎</Text>
              <Text style={styles.paymentText}>Apple Pay</Text>
              <Text style={styles.checkmark}>{paymentMethod === 'apple' ? '✓' : ''}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.paymentOption, paymentMethod === 'google' && styles.selectedPayment]}
              onPress={() => setPaymentMethod('google')}
            >
              <Text style={styles.paymentIcon}>🅖</Text>
              <Text style={styles.paymentText}>Google Pay</Text>
              <Text style={styles.checkmark}>{paymentMethod === 'google' ? '✓' : ''}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.termsSection}>
            <TouchableOpacity 
              style={styles.checkbox}
              onPress={() => setTermsAccepted(!termsAccepted)}
            >
              <Text style={styles.checkboxText}>{termsAccepted ? '☑️' : '☐'}</Text>
            </TouchableOpacity>
            <View style={styles.termsText}>
              <Text style={styles.termsLabel}>I accept the:</Text>
              <TouchableOpacity onPress={() => Alert.alert('Terms', 'Gold Membership Terms would be displayed here')}>
                <Text style={styles.termsLink}>• Gold Membership Terms</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => Alert.alert('Payment Terms', 'Payment Terms would be displayed here')}>
                <Text style={styles.termsLink}>• Payment Terms</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Flow:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/payment-processing')}
            >
              <Text style={styles.navButtonText}>→ Payment Processing Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/gold-confirmation')}
            >
              <Text style={styles.navButtonText}>→ Gold Membership Confirmation</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity 
              style={[styles.actionButton, styles.purchaseButton]}
              onPress={handlePurchase}
            >
              <Text style={styles.purchaseButtonText}>🏆 Purchase Gold Membership - €80</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.cancelButton]}
              onPress={() => router.back()}
            >
              <Text style={styles.cancelButtonText}>Maybe Later</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Secure payment processing • 30-day money-back guarantee
            </Text>
            <TouchableOpacity onPress={() => router.back()}>
              <Text style={styles.backText}>← Back to Profile</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  priceCard: {
    backgroundColor: '#fff3e0',
    padding: 30,
    borderRadius: 20,
    alignItems: 'center',
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  priceTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#e65100',
    marginBottom: 10,
  },
  price: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#ff9800',
    marginBottom: 5,
  },
  priceSubtitle: {
    fontSize: 16,
    color: '#f57c00',
  },
  benefitsSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  benefitsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  benefit: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitIcon: {
    fontSize: 20,
    marginRight: 15,
    width: 30,
  },
  benefitText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  paymentSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  paymentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    marginBottom: 10,
  },
  selectedPayment: {
    borderColor: '#2196F3',
    backgroundColor: '#e3f2fd',
  },
  paymentIcon: {
    fontSize: 24,
    marginRight: 15,
    width: 40,
  },
  paymentText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  checkmark: {
    fontSize: 20,
    color: '#2196F3',
    fontWeight: 'bold',
  },
  termsSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
  },
  checkbox: {
    marginRight: 10,
    marginTop: 2,
  },
  checkboxText: {
    fontSize: 20,
  },
  termsText: {
    flex: 1,
  },
  termsLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 5,
  },
  termsLink: {
    fontSize: 16,
    color: '#2196F3',
    marginBottom: 3,
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  actions: {
    marginBottom: 20,
  },
  actionButton: {
    paddingVertical: 18,
    paddingHorizontal: 20,
    borderRadius: 15,
    marginBottom: 10,
    alignItems: 'center',
  },
  purchaseButton: {
    backgroundColor: '#FFD700',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#999',
  },
  purchaseButtonText: {
    color: '#333',
    fontSize: 18,
    fontWeight: 'bold',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 15,
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
