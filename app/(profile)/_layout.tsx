import { Stack } from 'expo-router';

export default function ProfileLayout() {
  return (
    <Stack>
      <Stack.Screen name="user-center" options={{ headerShown: false }} />
      <Stack.Screen name="private-profile" options={{ headerShown: false }} />
      <Stack.Screen name="public-profile" options={{ headerShown: false }} />
      <Stack.Screen name="edit-private-profile" options={{ headerShown: false }} />
      <Stack.Screen name="edit-public-profile" options={{ headerShown: false }} />
      <Stack.Screen name="gold-membership" options={{ headerShown: false }} />
      <Stack.Screen name="wallet" options={{ headerShown: false }} />
      <Stack.Screen name="verification-request" options={{ headerShown: false }} />
      <Stack.Screen name="qr-display" options={{ headerShown: false }} />
      <Stack.Screen name="user-profile" options={{ headerShown: false }} />
      <Stack.Screen name="user-public-profile" options={{ headerShown: false }} />
      <Stack.Screen name="business-account-setup" options={{ headerShown: false }} />
      <Stack.Screen name="content-preferences" options={{ headerShown: false }} />
    </Stack>
  );
}
