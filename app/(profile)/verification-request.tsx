import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';

export default function VerificationRequestScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Verification Request</Text>
        <Text style={styles.subtitle}>Request account verification</Text>
        <Text style={styles.pricing}>Private: €14.49 | Public: €24.49 | Both: €40.49</Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
  },
  pricing: {
    fontSize: 14,
    color: '#2196F3',
    textAlign: 'center',
  },
});
