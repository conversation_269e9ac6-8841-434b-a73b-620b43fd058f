import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { router } from 'expo-router';

export default function CallsScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Calls</Text>
            <Text style={styles.subtitle}>Voice and video calling history</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Calls Tab</Text>
            <Text style={styles.descriptionText}>
              This tab displays recent call history with call type indicators (incoming/missed/outgoing), 
              timestamps, duration, and search functionality. Users can initiate new calls and 
              access group call options.
            </Text>
          </View>

          <View style={styles.componentsSection}>
            <Text style={styles.componentsTitle}>Screen Components:</Text>
            
            <View style={styles.component}>
              <Text style={styles.componentName}>📞 Recent Calls List</Text>
              <Text style={styles.componentDesc}>History of all voice and video calls</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📊 Call Type Indicators</Text>
              <Text style={styles.componentDesc}>Visual indicators for incoming/missed/outgoing calls</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>⏰ Timestamp & Duration</Text>
              <Text style={styles.componentDesc}>When calls occurred and how long they lasted</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>🔍 Search Functionality</Text>
              <Text style={styles.componentDesc}>Search through call history</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>➕ New Call Button</Text>
              <Text style={styles.componentDesc}>Floating button to initiate new calls</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(calls)/contact-selection')}
              >
                <Text style={styles.componentButtonText}>→ Contact Selection</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Options:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(calls)/initiate-call')}
            >
              <Text style={styles.navButtonText}>→ Initiate New Call</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(calls)/contact-selection')}
            >
              <Text style={styles.navButtonText}>→ Contact Selection Screen</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.mockContent}>
            <Text style={styles.mockTitle}>Recent Calls:</Text>
            
            <View style={styles.callItem}>
              <View style={styles.callIcon}>
                <Text style={styles.callIconText}>📞</Text>
              </View>
              <View style={styles.callInfo}>
                <Text style={styles.callName}>John Doe</Text>
                <View style={styles.callDetails}>
                  <Text style={styles.callType}>Outgoing</Text>
                  <Text style={styles.callTime}>Today, 2:30 PM</Text>
                </View>
                <Text style={styles.callDuration}>Duration: 5:23</Text>
              </View>
              <TouchableOpacity style={styles.callButton}>
                <Text style={styles.callButtonText}>📞</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.callItem}>
              <View style={[styles.callIcon, styles.missedCall]}>
                <Text style={styles.callIconText}>📞</Text>
              </View>
              <View style={styles.callInfo}>
                <Text style={styles.callName}>Alice Smith</Text>
                <View style={styles.callDetails}>
                  <Text style={[styles.callType, styles.missedText]}>Missed</Text>
                  <Text style={styles.callTime}>Yesterday, 6:45 PM</Text>
                </View>
                <Text style={styles.callDuration}>Missed call</Text>
              </View>
              <TouchableOpacity style={styles.callButton}>
                <Text style={styles.callButtonText}>📞</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.callItem}>
              <View style={styles.callIcon}>
                <Text style={styles.callIconText}>📹</Text>
              </View>
              <View style={styles.callInfo}>
                <Text style={styles.callName}>Team Meeting</Text>
                <View style={styles.callDetails}>
                  <Text style={styles.callType}>Group Video</Text>
                  <Text style={styles.callTime}>Yesterday, 10:00 AM</Text>
                </View>
                <Text style={styles.callDuration}>Duration: 45:12</Text>
              </View>
              <TouchableOpacity style={styles.callButton}>
                <Text style={styles.callButtonText}>📹</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.callItem}>
              <View style={styles.callIcon}>
                <Text style={styles.callIconText}>📞</Text>
              </View>
              <View style={styles.callInfo}>
                <Text style={styles.callName}>Mom</Text>
                <View style={styles.callDetails}>
                  <Text style={styles.callType}>Incoming</Text>
                  <Text style={styles.callTime}>2 days ago, 7:20 PM</Text>
                </View>
                <Text style={styles.callDuration}>Duration: 12:45</Text>
              </View>
              <TouchableOpacity style={styles.callButton}>
                <Text style={styles.callButtonText}>📞</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.createButton}>
            <TouchableOpacity 
              style={styles.floatingButton}
              onPress={() => router.push('/(calls)/contact-selection')}
            >
              <Text style={styles.floatingButtonText}>📞</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  componentsSection: {
    marginBottom: 20,
  },
  componentsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  component: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  componentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  componentDesc: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  componentButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    alignSelf: 'flex-start',
  },
  componentButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  mockContent: {
    marginBottom: 20,
  },
  mockTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  callItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  callIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  missedCall: {
    backgroundColor: '#f44336',
  },
  callIconText: {
    fontSize: 20,
  },
  callInfo: {
    flex: 1,
  },
  callName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  callDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  callType: {
    fontSize: 14,
    color: '#4CAF50',
    marginRight: 10,
    fontWeight: 'bold',
  },
  missedText: {
    color: '#f44336',
  },
  callTime: {
    fontSize: 12,
    color: '#999',
  },
  callDuration: {
    fontSize: 12,
    color: '#666',
  },
  callButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  callButtonText: {
    fontSize: 16,
  },
  createButton: {
    alignItems: 'flex-end',
    marginBottom: 20,
  },
  floatingButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingButtonText: {
    fontSize: 24,
  },
});
