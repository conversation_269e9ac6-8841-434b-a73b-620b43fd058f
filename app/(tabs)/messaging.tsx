import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { router } from 'expo-router';

export default function MessagingScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Messaging</Text>
            <Text style={styles.subtitle}>Secure end-to-end encrypted conversations</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Messaging Tab</Text>
            <Text style={styles.descriptionText}>
              This tab provides access to all messaging features including direct chats, 
              group conversations, and channel subscriptions. Features search, pinned chats, 
              recent chats, archived chats, and new message creation.
            </Text>
          </View>

          <View style={styles.componentsSection}>
            <Text style={styles.componentsTitle}>Screen Components:</Text>
            
            <View style={styles.component}>
              <Text style={styles.componentName}>🔍 Search Bar</Text>
              <Text style={styles.componentDesc}>Search through all conversations</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📌 Pinned Chats</Text>
              <Text style={styles.componentDesc}>Important conversations pinned to top</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>💬 Recent Chats</Text>
              <Text style={styles.componentDesc}>Latest conversations and messages</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(messaging)/chat-detail')}
              >
                <Text style={styles.componentButtonText}>→ Open Chat</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📦 Archived Chats</Text>
              <Text style={styles.componentDesc}>Swipe to access archived conversations</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>➕ New Message Button</Text>
              <Text style={styles.componentDesc}>Floating button to start new conversation</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(messaging)/new-message')}
              >
                <Text style={styles.componentButtonText}>→ New Message Menu</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Options:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(messaging)/chat-detail')}
            >
              <Text style={styles.navButtonText}>→ Chat Detail Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(messaging)/new-message')}
            >
              <Text style={styles.navButtonText}>→ New Message Menu</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/user-profile')}
            >
              <Text style={styles.navButtonText}>→ User Profile Screen</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.mockContent}>
            <Text style={styles.mockTitle}>Mock Chat List:</Text>
            
            <View style={styles.chatItem}>
              <View style={styles.chatAvatar}>
                <Text style={styles.avatarText}>JD</Text>
              </View>
              <View style={styles.chatInfo}>
                <Text style={styles.chatName}>John Doe</Text>
                <Text style={styles.chatMessage}>Hey, how are you doing?</Text>
                <Text style={styles.chatTime}>2 min ago</Text>
              </View>
              <View style={styles.chatStatus}>
                <Text style={styles.unreadBadge}>2</Text>
              </View>
            </View>

            <View style={styles.chatItem}>
              <View style={styles.chatAvatar}>
                <Text style={styles.avatarText}>GC</Text>
              </View>
              <View style={styles.chatInfo}>
                <Text style={styles.chatName}>Group Chat</Text>
                <Text style={styles.chatMessage}>Alice: Thanks for sharing!</Text>
                <Text style={styles.chatTime}>1 hour ago</Text>
              </View>
            </View>

            <View style={styles.chatItem}>
              <View style={styles.chatAvatar}>
                <Text style={styles.avatarText}>CH</Text>
              </View>
              <View style={styles.chatInfo}>
                <Text style={styles.chatName}>Tech Channel</Text>
                <Text style={styles.chatMessage}>New update available</Text>
                <Text style={styles.chatTime}>3 hours ago</Text>
              </View>
            </View>
          </View>

          <View style={styles.createButton}>
            <TouchableOpacity 
              style={styles.floatingButton}
              onPress={() => router.push('/(messaging)/new-message')}
            >
              <Text style={styles.floatingButtonText}>+</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  componentsSection: {
    marginBottom: 20,
  },
  componentsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  component: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  componentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  componentDesc: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  componentButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    alignSelf: 'flex-start',
  },
  componentButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  mockContent: {
    marginBottom: 20,
  },
  mockTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  chatItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  chatAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  chatInfo: {
    flex: 1,
  },
  chatName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  chatMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  chatTime: {
    fontSize: 12,
    color: '#999',
  },
  chatStatus: {
    alignItems: 'center',
  },
  unreadBadge: {
    backgroundColor: '#f44336',
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    textAlign: 'center',
  },
  createButton: {
    alignItems: 'flex-end',
    marginBottom: 20,
  },
  floatingButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingButtonText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
});
