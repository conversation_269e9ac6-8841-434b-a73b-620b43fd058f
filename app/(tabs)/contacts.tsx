import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { router } from 'expo-router';

export default function ContactsScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Contacts</Text>
            <Text style={styles.subtitle}>Your MEENA network connections</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Contacts Tab</Text>
            <Text style={styles.descriptionText}>
              This tab displays all user contacts with search functionality, alphabetical listing, 
              QR code access for easy contact sharing, online status indicators, and options to 
              add new contacts.
            </Text>
          </View>

          <View style={styles.componentsSection}>
            <Text style={styles.componentsTitle}>Screen Components:</Text>
            
            <View style={styles.component}>
              <Text style={styles.componentName}>🔍 Search Bar</Text>
              <Text style={styles.componentDesc}>Search through all contacts</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📝 Alphabetical Contact List</Text>
              <Text style={styles.componentDesc}>Contacts organized alphabetically</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📱 QR Code Access</Text>
              <Text style={styles.componentDesc}>Quick access to QR code scanning</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(contacts)/qr-scan')}
              >
                <Text style={styles.componentButtonText}>→ QR Code Scan</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>🟢 Online Status Indicators</Text>
              <Text style={styles.componentDesc}>See who's currently online</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>➕ Add Contact Button</Text>
              <Text style={styles.componentDesc}>Floating button to add new contacts</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(contacts)/add-contact')}
              >
                <Text style={styles.componentButtonText}>→ Add Contact</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Options:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/user-profile')}
            >
              <Text style={styles.navButtonText}>→ User Profile Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(contacts)/qr-scan')}
            >
              <Text style={styles.navButtonText}>→ QR Code Scan Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(contacts)/add-contact')}
            >
              <Text style={styles.navButtonText}>→ Add Contact Screen</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.mockContent}>
            <Text style={styles.mockTitle}>Contact List:</Text>
            
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>A</Text>
            </View>
            
            <View style={styles.contactItem}>
              <View style={styles.contactAvatar}>
                <Text style={styles.avatarText}>AS</Text>
              </View>
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>Alice Smith</Text>
                <Text style={styles.contactStatus}>@alice_smith • Online</Text>
              </View>
              <View style={styles.onlineIndicator} />
            </View>

            <View style={styles.contactItem}>
              <View style={styles.contactAvatar}>
                <Text style={styles.avatarText}>AB</Text>
              </View>
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>Alex Brown</Text>
                <Text style={styles.contactStatus}>@alex_b • Last seen 2h ago</Text>
              </View>
            </View>

            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>J</Text>
            </View>

            <View style={styles.contactItem}>
              <View style={styles.contactAvatar}>
                <Text style={styles.avatarText}>JD</Text>
              </View>
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>John Doe</Text>
                <Text style={styles.contactStatus}>@john_doe • Online</Text>
              </View>
              <View style={styles.onlineIndicator} />
            </View>

            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>M</Text>
            </View>

            <View style={styles.contactItem}>
              <View style={styles.contactAvatar}>
                <Text style={styles.avatarText}>MJ</Text>
              </View>
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>Mary Johnson</Text>
                <Text style={styles.contactStatus}>@mary_j • Last seen yesterday</Text>
              </View>
            </View>
          </View>

          <View style={styles.qrSection}>
            <TouchableOpacity 
              style={styles.qrButton}
              onPress={() => router.push('/(contacts)/qr-scan')}
            >
              <Text style={styles.qrButtonText}>📱 Scan QR Code</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.createButton}>
            <TouchableOpacity 
              style={styles.floatingButton}
              onPress={() => router.push('/(contacts)/add-contact')}
            >
              <Text style={styles.floatingButtonText}>+</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  componentsSection: {
    marginBottom: 20,
  },
  componentsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  component: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  componentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  componentDesc: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  componentButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    alignSelf: 'flex-start',
  },
  componentButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  mockContent: {
    marginBottom: 20,
  },
  mockTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  sectionHeader: {
    backgroundColor: '#e0e0e0',
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginBottom: 5,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  contactItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 5,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  contactAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  contactStatus: {
    fontSize: 14,
    color: '#666',
  },
  onlineIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
  },
  qrSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  qrButton: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 10,
  },
  qrButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  createButton: {
    alignItems: 'flex-end',
    marginBottom: 20,
  },
  floatingButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingButtonText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
});
