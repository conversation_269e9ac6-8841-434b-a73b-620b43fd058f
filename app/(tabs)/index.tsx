import { router } from 'expo-router';
import React from 'react';
import { Safe<PERSON>reaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function HomeDiscoverScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Home / Discover</Text>
            <Text style={styles.subtitle}>Your social feed and discovery hub</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Home/Discover Tab</Text>
            <Text style={styles.descriptionText}>
              This is the main social feed where users discover content from followed users,
              view stories, and explore trending hashtags. Features include stories carousel,
              public feed, advertising bar, hashtag filters, and floating create button.
            </Text>
          </View>

          <View style={styles.componentsSection}>
            <Text style={styles.componentsTitle}>Screen Components:</Text>

            <View style={styles.component}>
              <Text style={styles.componentName}>📖 Stories Carousel</Text>
              <Text style={styles.componentDesc}>Top section with user stories</Text>
              <TouchableOpacity
                style={styles.componentButton}
                onPress={() => router.push('/(stories)/story-viewing')}
              >
                <Text style={styles.componentButtonText}>→ View Stories</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📱 Public Feed</Text>
              <Text style={styles.componentDesc}>Posts from followed users</Text>
              <TouchableOpacity
                style={styles.componentButton}
                onPress={() => router.push('/(stories)/post-detail')}
              >
                <Text style={styles.componentButtonText}>→ View Post Details</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📢 Advertising Bar</Text>
              <Text style={styles.componentDesc}>Bottom advertising section</Text>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>#️⃣ Hashtag Filters</Text>
              <Text style={styles.componentDesc}>Top navigation filters</Text>
              <TouchableOpacity
                style={styles.componentButton}
                onPress={() => router.push('/(stories)/hashtag-feed')}
              >
                <Text style={styles.componentButtonText}>→ Hashtag Feed</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>➕ Floating Create Button</Text>
              <Text style={styles.componentDesc}>Circular button for content creation</Text>
              <TouchableOpacity
                style={styles.componentButton}
                onPress={() => router.push('/(stories)/story-creation')}
              >
                <Text style={styles.componentButtonText}>→ Create Story/Post</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  componentsSection: {
    marginBottom: 20,
  },
  componentsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  component: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  componentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  componentDesc: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  componentButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    alignSelf: 'flex-start',
  },
  componentButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
