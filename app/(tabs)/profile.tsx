import { router } from 'expo-router';
import React, { useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function ProfileScreen() {
  const [isPrivateProfile, setIsPrivateProfile] = useState(true);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Profile</Text>
            <Text style={styles.subtitle}>Your MEENA identity and settings</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Profile Tab</Text>
            <Text style={styles.descriptionText}>
              This tab provides access to user profile management with dual-profile system 
              (Private/Public), Gold Membership status, QR code access, settings, and wallet 
              balance display. Users can toggle between their private and public profiles.
            </Text>
          </View>

          <View style={styles.profileToggle}>
            <Text style={styles.toggleTitle}>Profile View:</Text>
            <View style={styles.toggleButtons}>
              <TouchableOpacity 
                style={[styles.toggleButton, isPrivateProfile && styles.activeToggle]}
                onPress={() => setIsPrivateProfile(true)}
              >
                <Text style={[styles.toggleText, isPrivateProfile && styles.activeToggleText]}>
                  Private
                </Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.toggleButton, !isPrivateProfile && styles.activeToggle]}
                onPress={() => setIsPrivateProfile(false)}
              >
                <Text style={[styles.toggleText, !isPrivateProfile && styles.activeToggleText]}>
                  Public
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.profileSummary}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>ME</Text>
            </View>
            <Text style={styles.profileName}>
              {isPrivateProfile ? 'Your Private Profile' : 'Your Public Profile'}
            </Text>
            <Text style={styles.profileId}>
              {isPrivateProfile ? 'ID: ABC123XYZ' : '@your_username'}
            </Text>
            <View style={styles.badges}>
              <View style={styles.goldBadge}>
                <Text style={styles.badgeText}>🏆 Gold Member</Text>
              </View>
              <View style={styles.verifiedBadge}>
                <Text style={styles.badgeText}>✓ Verified</Text>
              </View>
            </View>
          </View>

          <View style={styles.componentsSection}>
            <Text style={styles.componentsTitle}>Profile Components:</Text>
            
            <View style={styles.component}>
              <Text style={styles.componentName}>👤 Profile Summary</Text>
              <Text style={styles.componentDesc}>Dual-view toggle between Private and Public</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push(isPrivateProfile ? '/(profile)/private-profile' : '/(profile)/public-profile')}
              >
                <Text style={styles.componentButtonText}>
                  → {isPrivateProfile ? 'Private Profile' : 'Public Profile'}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>🏆 Gold Membership Badge</Text>
              <Text style={styles.componentDesc}>Premium membership status indicator</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(profile)/gold-membership')}
              >
                <Text style={styles.componentButtonText}>→ Gold Membership</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>📱 QR Code Access</Text>
              <Text style={styles.componentDesc}>Quick access to your QR code</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(profile)/qr-display')}
              >
                <Text style={styles.componentButtonText}>→ QR Code Display</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>⚙️ Settings Access</Text>
              <Text style={styles.componentDesc}>Access to all app settings</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(security)/settings-main')}
              >
                <Text style={styles.componentButtonText}>→ Settings Main</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.component}>
              <Text style={styles.componentName}>💰 Wallet Balance</Text>
              <Text style={styles.componentDesc}>View and manage your wallet</Text>
              <TouchableOpacity 
                style={styles.componentButton}
                onPress={() => router.push('/(profile)/wallet')}
              >
                <Text style={styles.componentButtonText}>→ Wallet Screen</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Navigation Options:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/private-profile')}
            >
              <Text style={styles.navButtonText}>→ Private Profile Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/public-profile')}
            >
              <Text style={styles.navButtonText}>→ Public Profile Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/gold-membership')}
            >
              <Text style={styles.navButtonText}>→ Gold Membership Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(profile)/wallet')}
            >
              <Text style={styles.navButtonText}>→ Wallet Screen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(security)/settings-main')}
            >
              <Text style={styles.navButtonText}>→ Settings Main Screen</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.walletSection}>
            <Text style={styles.walletTitle}>Wallet Balance</Text>
            <Text style={styles.walletBalance}>€25.50</Text>
            <TouchableOpacity 
              style={styles.walletButton}
              onPress={() => router.push('/(profile)/wallet')}
            >
              <Text style={styles.walletButtonText}>Manage Wallet</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.quickActions}>
            <Text style={styles.quickActionsTitle}>Quick Actions:</Text>
            
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/(profile)/qr-display')}
            >
              <Text style={styles.actionButtonText}>📱 Show My QR Code</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/(security)/settings-main')}
            >
              <Text style={styles.actionButtonText}>⚙️ Settings</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/(profile)/verification-request')}
            >
              <Text style={styles.actionButtonText}>✓ Request Verification</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  profileToggle: {
    marginBottom: 20,
    alignItems: 'center',
  },
  toggleTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  toggleButtons: {
    flexDirection: 'row',
    backgroundColor: '#e0e0e0',
    borderRadius: 25,
    padding: 4,
  },
  toggleButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  activeToggle: {
    backgroundColor: '#2196F3',
  },
  toggleText: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  activeToggleText: {
    color: 'white',
  },
  profileSummary: {
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatarText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  profileId: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  badges: {
    flexDirection: 'row',
    gap: 10,
  },
  goldBadge: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  verifiedBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  componentsSection: {
    marginBottom: 20,
  },
  componentsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  component: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  componentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  componentDesc: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  componentButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    alignSelf: 'flex-start',
  },
  componentButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  walletSection: {
    backgroundColor: '#fff3e0',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 20,
  },
  walletTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e65100',
    marginBottom: 10,
  },
  walletBalance: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ff9800',
    marginBottom: 15,
  },
  walletButton: {
    backgroundColor: '#ff9800',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 10,
  },
  walletButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  quickActions: {
    marginBottom: 20,
  },
  quickActionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  actionButton: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionButtonText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
});
