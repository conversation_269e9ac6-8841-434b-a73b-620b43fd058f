import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { router } from 'expo-router';

export default function SettingsMainScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Settings</Text>
            <Text style={styles.subtitle}>Manage your MEENA experience</Text>
          </View>

          <View style={styles.description}>
            <Text style={styles.descriptionTitle}>Main Settings Screen</Text>
            <Text style={styles.descriptionText}>
              Central hub for all app settings including security, privacy, account management, 
              content preferences, and system configurations. Provides access to all major 
              setting categories with clear navigation paths.
            </Text>
          </View>

          <View style={styles.settingsSection}>
            <Text style={styles.sectionTitle}>Account & Profile</Text>
            
            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(profile)/private-profile')}
            >
              <Text style={styles.settingIcon}>👤</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Private Profile</Text>
                <Text style={styles.settingDesc}>Manage your private profile settings</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(profile)/public-profile')}
            >
              <Text style={styles.settingIcon}>🌐</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Public Profile</Text>
                <Text style={styles.settingDesc}>Manage your public profile settings</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(security)/account-management')}
            >
              <Text style={styles.settingIcon}>⚙️</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Account Management</Text>
                <Text style={styles.settingDesc}>Account settings and preferences</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.settingsSection}>
            <Text style={styles.sectionTitle}>Security & Privacy</Text>
            
            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(security)/security-settings')}
            >
              <Text style={styles.settingIcon}>🔒</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Security Settings</Text>
                <Text style={styles.settingDesc}>2FA, passwords, encryption</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(security)/privacy-settings')}
            >
              <Text style={styles.settingIcon}>🛡️</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Privacy Settings</Text>
                <Text style={styles.settingDesc}>Control who can see your content</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(security)/remote-wipe-system')}
            >
              <Text style={styles.settingIcon}>🗑️</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Remote Wipe</Text>
                <Text style={styles.settingDesc}>Emergency account deletion</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.settingsSection}>
            <Text style={styles.sectionTitle}>Content & Moderation</Text>
            
            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(profile)/content-preferences')}
            >
              <Text style={styles.settingIcon}>🎨</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Content Preferences</Text>
                <Text style={styles.settingDesc}>Customize your content experience</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(security)/content-moderation')}
            >
              <Text style={styles.settingIcon}>🚫</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Content Moderation</Text>
                <Text style={styles.settingDesc}>Reporting and moderation tools</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.settingsSection}>
            <Text style={styles.sectionTitle}>Premium Features</Text>
            
            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(profile)/gold-membership')}
            >
              <Text style={styles.settingIcon}>🏆</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Gold Membership</Text>
                <Text style={styles.settingDesc}>Upgrade to premium features</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.settingItem}
              onPress={() => router.push('/(profile)/verification-request')}
            >
              <Text style={styles.settingIcon}>✓</Text>
              <View style={styles.settingInfo}>
                <Text style={styles.settingName}>Verification</Text>
                <Text style={styles.settingDesc}>Request account verification</Text>
              </View>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.navigation}>
            <Text style={styles.navigationTitle}>Available Settings Categories:</Text>
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(security)/security-settings')}
            >
              <Text style={styles.navButtonText}>→ Security Settings</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(security)/privacy-settings')}
            >
              <Text style={styles.navButtonText}>→ Privacy Settings</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.navButton}
              onPress={() => router.push('/(security)/account-management')}
            >
              <Text style={styles.navButtonText}>→ Account Management</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity onPress={() => router.back()}>
              <Text style={styles.backText}>← Back to Profile</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e8f5e8',
    borderRadius: 10,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2e7d32',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4caf50',
  },
  settingsSection: {
    backgroundColor: '#fff',
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    padding: 20,
    paddingBottom: 10,
    backgroundColor: '#f8f9fa',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingIcon: {
    fontSize: 24,
    marginRight: 15,
    width: 30,
  },
  settingInfo: {
    flex: 1,
  },
  settingName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  settingDesc: {
    fontSize: 14,
    color: '#666',
  },
  arrow: {
    fontSize: 18,
    color: '#999',
  },
  navigation: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
  },
  navigationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1976d2',
  },
  navButton: {
    paddingVertical: 5,
  },
  navButtonText: {
    fontSize: 14,
    color: '#1976d2',
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  backText: {
    color: '#666',
    fontSize: 16,
  },
});
