import { Stack } from 'expo-router';

export default function SecurityLayout() {
  return (
    <Stack>
      <Stack.Screen name="settings-main" options={{ headerShown: false }} />
      <Stack.Screen name="security-settings" options={{ headerShown: false }} />
      <Stack.Screen name="privacy-settings" options={{ headerShown: false }} />
      <Stack.Screen name="account-management" options={{ headerShown: false }} />
      <Stack.Screen name="remote-wipe-system" options={{ headerShown: false }} />
      <Stack.Screen name="account-recovery" options={{ headerShown: false }} />
      <Stack.Screen name="content-moderation" options={{ headerShown: false }} />
      <Stack.Screen name="report-content" options={{ headerShown: false }} />
      <Stack.Screen name="verification-setup" options={{ headerShown: false }} />
      <Stack.Screen name="password-change" options={{ headerShown: false }} />
      <Stack.Screen name="remote-wipe-setup" options={{ headerShown: false }} />
    </Stack>
  );
}
