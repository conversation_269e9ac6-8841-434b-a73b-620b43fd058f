# MEENA SOCIAL NETWORK - FRONTEND STRUCTURE SPECIFICATION

## VERSION 1.0

**Document Control**
- **Prepared For**: MEENA Development Team
- **Prepared By**: Product Design Team
- **Date**: October 26, 2023
- **Status**: Approved for Development

---

## 1. INTRODUCTION

This document specifies the complete frontend structure for the MEENA Social Network application, detailing all user-facing screens, navigation flows, and component specifications. The structure has been designed to fully implement all functional requirements while providing an intuitive user experience that balances security, privacy, and social networking capabilities.

This specification serves as the definitive reference for UI/UX design, frontend development, and quality assurance testing of the MEENA application.

---

## 2. APPLICATION OVERVIEW

MEENA is a secure social networking and messaging platform combining end-to-end encrypted messaging with public social networking features. The application features a dual-profile system (private Telegram-style profile and public social media profile) with comprehensive content creation, group management, and security features.

### Key Features Implemented
- Secure messaging with end-to-end encryption
- Dual-profile system (private and public)
- Group and channel creation with multiple privacy levels
- Rich story functionality with media layering
- Gold Membership with lifetime benefits
- Advanced security and account recovery systems
- Content moderation and reporting tools

---

## 3. AUTHENTICATION FLOW

### 3.1 Onboarding Screen
**Components:**
- Login option
- Sign Up option
- Language selection
- Biometric login option (for returning users)
- App branding and tagline

**Navigation:**
- Login option → Login Screen
- Sign Up option → Registration Screen

### 3.2 Login Screen
**Components:**
- ID input field (9-character alphanumeric)
- Password input field
- "Forgot Password" link
- "Create Account" link
- Biometric authentication option (if available)
- Language selector

**Navigation:**
- Successful login → Main App Screen
- "Forgot Password" → Password Recovery Screen
- "Create Account" → Registration Screen

### 3.3 Registration Screen
**Components:**
- Phone number or email input
- Name input
- Auto-generated unique 9-character ID display
- Password setup (or biometric option)
- Secret Phrase Generation button
- Terms acceptance checkbox with links to:
  - Community Rules
  - Terms of Use
  - Privacy Policy

**Navigation:**
- Next → ID Generation Screen

### 3.4 ID Generation Screen
**Components:**
- Display of unique 9-character alphanumeric ID
- ID copy button
- Explanation of ID purpose and usage
- "Continue" button

**Navigation:**
- Continue → Secret Phrase Setup Screen

### 3.5 Secret Phrase Setup Screen
**Components:**
- 9 randomly generated African language words (Swahili, Zulu, Kikongo, etc.)
- Phrase display with copy functionality
- Phrase confirmation input
- Security warning about phrase importance
- "I have saved my phrase" checkbox

**Navigation:**
- Successful confirmation → Login Screen
- Incomplete confirmation → Warning message

### 3.6 Password Recovery Screen
**Components:**
- ID input field
- Secret Phrase verification
- PIN verification (if set)
- "Recover Account" button

**Navigation:**
- Successful verification → Password Reset Screen
- Failed verification → Error message with retry option

---

## 4. MAIN NAVIGATION STRUCTURE

The application uses a 5-tab bottom navigation system for primary access to core functionality.

### 4.1 Home/Discover Tab
**Components:**
- Stories carousel (top section)
- Public feed (posts from followed users)
- Advertising bar (bottom)
- Hashtag filters (top navigation)
- Floating "Create" button (circular)

**Navigation:**
- Story tap → Story Viewing Screen
- Post tap → Post Detail Screen
- Hashtag tap → Hashtag Feed Screen
- Floating "Create" button → Story/Post Composer Screen
- Profile tap → User Public Profile Screen

### 4.2 Messaging Tab
**Components:**
- Search bar
- Pinned chats section
- Recent chats section
- Archived chats section (swipe to access)
- Floating "New Message" button

**Navigation:**
- Chat tap → Chat Detail Screen
- New Message button → New Message Menu
- Contact tap → User Profile Screen

### 4.3 Calls Tab
**Components:**
- Recent calls list
- Call type indicators (incoming/missed/outgoing)
- Timestamp and duration
- Search functionality
- Floating "New Call" button

**Navigation:**
- Call entry tap → Initiate new call
- New Call button → Contact Selection Screen

### 4.4 Contacts Tab
**Components:**
- Search bar
- Alphabetical contact list
- QR code access button
- Online status indicators
- Floating "Add Contact" button

**Navigation:**
- Contact tap → User Profile Screen
- QR code button → QR Code Scan Screen
- Add Contact button → Add Contact Screen

### 4.5 Profile Tab
**Components:**
- User profile summary (dual-view toggle)
- Gold Membership badge (if applicable)
- QR code access
- Settings access button
- Wallet balance display

**Navigation:**
- Profile toggle → Switch between Private and Public profiles
- Gold badge → Gold Membership Screen
- Settings button → Settings Main Screen
- QR code → QR Code Display Screen

---

## 5. PROFILE MANAGEMENT SYSTEM

### 5.1 User Center
**Components:**
- Profile toggle (Private/Public)
- Gold Membership indicator
- Wallet balance
- Verification status
- Quick access to key features

**Navigation:**
- Private Profile → Private Profile Screen
- Public Profile → Public Profile Screen
- Gold Membership → Gold Membership Screen
- Wallet → Wallet Screen
- Verification → Verification Request Screen

### 5.2 Private Profile Screen (Telegram-style)
**Components:**
- Profile photo
- Display name
- Status (online/last seen)
- ID display
- Contact information (phone, email)
- Security phrase indicator
- Gold Membership badge (if applicable)

**Navigation:**
- Edit button → Edit Private Profile Screen
- Security settings → Security Settings Screen
- Privacy settings → Privacy Settings Screen
- Verification request → Verification Request Screen

### 5.3 Public Profile Screen (Social Media-style)
**Components:**
- Cover photo
- Profile photo
- Display name
- PID (username)
- Bio
- Website links
- Follower/following counts
- Content posts grid
- Verification badge (if applicable)
- Gold Membership badge (if applicable)

**Navigation:**
- Edit button → Edit Public Profile Screen
- Content preferences → Content Preferences Screen
- Business setup → Business Account Setup Screen
- Verification request → Verification Request Screen

### 5.4 Gold Membership Screen
**Components:**
- Benefits list:
  - Unlimited group/channel creation
  - Unlimited calls
  - Remote profile deletion
  - Unsubscribe from default accounts
- Payment amount (€80 one-time)
- Payment method selection
- Terms acceptance

**Navigation:**
- Payment confirmation → Payment Processing Screen
- Successful payment → Gold Membership Confirmation Screen

---

## 6. GROUP & CHANNEL CREATION

### 6.1 Group Creation Flow
**Components:**
- Group type selection:
  - Public Group
  - Private Group
  - Secret Group (with description: "Less restriction, more freedom but community rules still apply")
- Terms acceptance checkbox
- Payment information (if applicable):
  - Free: 2 public/private groups
  - €2.49: Additional public/private group
  - €6.49: Secret group (one-time)

**Navigation:**
- Type selection → Terms Acceptance Screen
- Terms acceptance → Payment Screen (if required)
- Payment completion → Group Management Screen

### 6.2 Channel Creation Flow
**Components:**
- Channel type selection:
  - Public Channel
  - Private Channel
  - Secret Channel (with description)
- Terms acceptance checkbox
- Payment information (if applicable):
  - Free: 2 public/private channels
  - €4.49: Additional public/private channel
  - €12.49: Secret channel (one-time)
- Group linking option

**Navigation:**
- Type selection → Terms Acceptance Screen
- Terms acceptance → Payment Screen (if required)
- Payment completion → Channel Management Screen

### 6.3 Group Management Screen
**Components:**
- Group information (name, photo, description)
- Member list with roles
- Admin controls:
  - Edit group
  - Manage permissions
  - Add admins
  - Invite link management
- Subgroup creation option (free)
- Call options (audio/video, screen sharing)
- Reporting functionality

**Navigation:**
- Edit → Edit Group Screen
- Member → User Profile Screen
- Subgroup creation → Subgroup Creation Screen
- Report → Report Content Screen

### 6.4 Subgroup Creation Screen
**Components:**
- Subgroup name input
- Parent group selection
- Description field
- Creation confirmation

**Navigation:**
- Creation confirmation → Group Management Screen (with new subgroup)

---

## 7. STORIES WORKFLOW

### 7.1 Story Creation Flow
**Components:**
- Media selection (camera/gallery)
- Editing tools:
  - Text overlay
  - Media layering (photo on photo, photo on video, etc.)
  - Background selection
  - Music integration
  - Location tagging (city, neighborhood, venue)
- Privacy settings:
  - Private Profile only
  - Public Page only
  - Both
- Preview option

**Navigation:**
- Media selection → Editing Screen
- Editing → Privacy Settings Screen
- Privacy settings → Preview Screen
- Preview → Confirmation Screen

### 7.2 Story Viewing Screen
**Components:**
- Full-screen story display
- Progress indicator
- Reaction options
- Reply functionality
- Profile access
- Reporting option

**Navigation:**
- Swipe → Next story
- Reply → Direct Message Screen
- Profile → User Profile Screen
- Report → Report Content Screen

---

## 8. SECURITY & ACCOUNT MANAGEMENT

### 8.1 Security Settings Screen
**Components:**
- Two-Factor Authentication options:
  - Google Authenticator
  - Phone verification
  - Email verification
- Password change option
- Remote wipe setup
- Encryption settings

**Navigation:**
- 2FA selection → Verification Setup Screen
- Password change → Password Change Screen
- Remote wipe → Remote Wipe Setup Screen

### 8.2 Remote Wipe System
**Components:**
- PIN creation interface
- Old account ID input
- Secret phrase verification
- Confirmation with PIN

**Navigation:**
- Setup completion → PIN Confirmation Screen
- Wipe initiation → Account Deletion Confirmation

### 8.3 Account Recovery System
**Components:**
- Secret phrase verification (9 African language words)
- ID verification
- PIN verification (if set)
- Recovery confirmation

**Navigation:**
- Successful verification → Password Reset Screen
- Failed verification → Error message with retry option

### 8.4 Content Moderation & Reporting
**Components:**
- Report reason selection:
  - Racism
  - Sexual Content
  - Threats
  - Violence
  - Hate Speech
  - Terrorism
  - Harassment
- Evidence upload
- Additional details
- Submission confirmation

**Navigation:**
- Reason selection → Evidence Upload Screen
- Evidence upload → Confirmation Screen

---

## 9. COMPREHENSIVE NAVIGATION MAP

```
MEENA SOCIAL NETWORK - COMPLETE NAVIGATION
│
├── AUTHENTICATION FLOW
│   ├── Onboarding Screen
│   ├── Login Screen
│   ├── Registration Screen
│   ├── ID Generation Screen
│   ├── Secret Phrase Setup Screen
│   └── Password Recovery Screen
│
├── MAIN NAVIGATION (BOTTOM TABS)
│   ├── Home/Discover Tab
│   │   ├── Stories Carousel
│   │   ├── Public Feed
│   │   ├── Advertising Bar
│   │   ├── Hashtag Filters
│   │   └── Create Button
│   │
│   ├── Messaging Tab
│   │   ├── Chats List
│   │   │   ├── Direct Chats
│   │   │   ├── Group Chats
│   │   │   └── Channel Subscriptions
│   │   └── New Message Button
│   │
│   ├── Calls Tab
│   │   ├── Recent Calls
│   │   ├── New Call Button
│   │   └── Group Call Options
│   │
│   ├── Contacts Tab
│   │   ├── Contact List
│   │   ├── QR Code Access
│   │   └── Add Contact Button
│   │
│   └── Profile Tab
│       ├── User Profile
│       ├── Gold Membership Badge
│       └── Settings Access
│
├── PROFILE MANAGEMENT
│   ├── User Center
│   │   ├── Private Profile
│   │   │   ├── Edit Profile
│   │   │   ├── Security Settings
│   │   │   └── Verification Request
│   │   └── Public Profile
│   │       ├── Edit Profile
│   │       ├── Content Preferences
│   │       └── Verification Request
│   ├── Gold Membership Screen
│   └── Wallet Screen
│
├── GROUP & CHANNEL SYSTEM
│   ├── Group Creation Flow
│   │   ├── Group Type Selection
│   │   ├── Terms Acceptance
│   │   ├── Payment Screen
│   │   └── Group Management
│   │       ├── Subgroup Creation
│   │       └── Admin Controls
│   │
│   └── Channel Creation Flow
│       ├── Channel Type Selection
│       ├── Terms Acceptance
│       ├── Payment Screen
│       └── Channel Management
│
├── STORIES WORKFLOW
│   ├── Story Creation Flow
│   │   ├── Media Selection
│   │   ├── Editing Screen
│   │   ├── Privacy Settings
│   │   └── Preview
│   └── Story Viewing Screen
│
└── SECURITY & SETTINGS
    ├── Main Settings Screen
    ├── Security Settings
    ├── Privacy Settings
    ├── Content Preferences
    ├── Account Management
    ├── Remote Wipe System
    └── Account Recovery System
```

---

## 10. IMPLEMENTATION REQUIREMENTS

### 10.1 Security Requirements
- All messaging must use end-to-end encryption
- Secret phrase recovery must use 9 randomly generated African language words
- Remote wipe functionality must require PIN verification
- Account recovery must be possible within 60 days of deletion

### 10.2 Content Moderation
- Public/private groups/channels must have automated content moderation for prohibited content
- Secret groups/channels must have manual moderation for serious violations
- Reporting system must include all specified categories with evidence upload

### 10.3 Payment System
- Gold Membership: €80 one-time payment
- Group creation: €2.49 per additional public/private group, €6.49 for secret group
- Channel creation: €4.49 per additional public/private channel, €12.49 for secret channel
- Verification: €14.49 (private), €24.49 (public), €40.49 (both)

### 10.4 Version 01 Restrictions
- The following features must display "service momentanément indisponible" when accessed:
  - Business Account Setup
  - Content Preferences
  - Advanced moderation tools
  - Certain premium features not yet implemented

### 10.5 Technical Specifications
- ID system: 9-character alphanumeric, globally unique
- Secret phrase languages: Swahili, Zulu, Kikongo, Shona, Yoruba, Fon, Bambara, Sango
- Dual-profile system: Must maintain separation between private and public profiles
- Subgroup functionality: Must support free nested subgroups under main groups

---

## 11. NEXT STEPS FOR DEVELOPMENT

1. Create detailed wireframes for each screen based on this structure
2. Develop component library for consistent UI implementation
3. Prioritize screens for Version 01 implementation
4. Implement payment integration points with correct pricing structure
5. Build content moderation system with specified detection capabilities
6. Create secret phrase generation system using required African languages
7. Implement Gold Membership benefits system
8. Configure version 01 feature restrictions as specified

---

*This document is the property of MEENA and contains proprietary information. Unauthorized distribution is prohibited.*